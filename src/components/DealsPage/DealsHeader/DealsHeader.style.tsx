import styled from '@emotion/styled';
import isPropValid from '@emotion/is-prop-valid';
import { Card, Flex, Box, Heading } from '@qga/roo-ui/components';
import { themeGet } from 'styled-system';
import { rem } from 'polished';
import { PAYWITH_TOGGLE_ENABLED } from 'config';

type OptionsWrapperProps = {
  hasCampaignBanner: boolean;
  hasGlobalCampaign?: boolean;
  hasLocalCampaign?: boolean;
};

export const DealsHeaderWrapper = styled(Box)`
  position: relative;

  ${themeGet('mediaQueries.1')} {
    margin-bottom: ${rem(PAYWITH_TOGGLE_ENABLED ? 148 : 110)};
    min-height: ${rem(320)};
  }
`;

export const DealsTitle = styled(Heading.h1)`
  width: 100%;
  marginbottom: 1px;
  ${themeGet('textStyles.h1')};
`;

export const OptionsWrapper = styled(Flex, { shouldForwardProp: isPropValid })<OptionsWrapperProps>`
  width: 100%;

  ${themeGet('mediaQueries.1')} {
    position: absolute;
    top: ${({ hasCampaignBanner, hasGlobalCampaign, hasLocalCampaign }) => {
      // Base position when no campaigns are active
      let topPosition = 148;

      // Add space for local campaign banner (blue banner in DealsHeader)
      if (hasLocalCampaign) {
        topPosition += 168; // Original difference between 316 and 148
      }

      // Add space for global campaign banner (CampaignMessaging)
      if (hasGlobalCampaign) {
        topPosition += 40;
      }

      // Fallback to original logic if new props aren't provided
      if (hasGlobalCampaign === undefined && hasLocalCampaign === undefined) {
        topPosition = hasCampaignBanner ? 316 : 148;
      }

      return rem(topPosition);
    }};
  }
`;

export const DesktopCard = styled(Card)`
  width: 100%;
  margin: 0 auto;
  box-shadow: none;
  padding: 0;
  background: transparent;
  max-width: calc(${themeGet('maxWidths.default')} - ${themeGet('space.6')});

  ${themeGet('mediaQueries.2')} {
    box-shadow: ${themeGet('shadows.default')};
    padding: ${themeGet('space.6')} ${themeGet('space.8')};
    background: ${themeGet('colors.white')};
  }
`;

export const MobileCard = styled(Card)`
  @media (max-width: ${themeGet('breakpoints.0')}) {
    overflow-x: hidden;
  }

  ${themeGet('mediaQueries.1')} {
    border-radius: 0;
    border-bottom: 1px solid white;
    padding: ${themeGet('space.8')} ${themeGet('space.4')};
  }

  ${themeGet('mediaQueries.2')} {
    box-shadow: none;
    padding: 0;
  }
`;
